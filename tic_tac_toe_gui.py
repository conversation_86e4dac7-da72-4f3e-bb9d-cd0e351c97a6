import tkinter as tk
from tkinter import messagebox
import random

class TicTacToeGame:
    def __init__(self):
        """Initialize the Tic-Tac-Toe game with GUI setup."""
        self.root = tk.Tk()
        self.root.title("Tic-Tac-Toe Game")
        self.root.geometry("400x500")
        self.root.resizable(False, False)
        
        # Game state variables
        self.board = [["" for _ in range(3)] for _ in range(3)]
        self.current_player = "X"  # Player is always X
        self.computer_player = "O"  # Computer is always O
        self.game_over = False
        
        # Score tracking
        self.player_score = 0
        self.computer_score = 0
        self.ties = 0
        
        # Create GUI elements
        self.create_widgets()
        
    def create_widgets(self):
        """Create and arrange all GUI widgets."""
        # Title label
        title_label = tk.Label(
            self.root, 
            text="Tic-Tac-Toe", 
            font=("Arial", 20, "bold"),
            pady=10
        )
        title_label.pack()
        
        # Score display
        self.score_frame = tk.Frame(self.root)
        self.score_frame.pack(pady=5)
        
        self.score_label = tk.Label(
            self.score_frame,
            text=f"Player (X): {self.player_score} | Computer (O): {self.computer_score} | Ties: {self.ties}",
            font=("Arial", 12)
        )
        self.score_label.pack()
        
        # Game status label
        self.status_label = tk.Label(
            self.root,
            text="Your turn! Click a cell to place X",
            font=("Arial", 12),
            pady=5
        )
        self.status_label.pack()
        
        # Game board frame
        self.board_frame = tk.Frame(self.root)
        self.board_frame.pack(pady=10)
        
        # Create 3x3 grid of buttons
        self.buttons = []
        for i in range(3):
            button_row = []
            for j in range(3):
                button = tk.Button(
                    self.board_frame,
                    text="",
                    font=("Arial", 20, "bold"),
                    width=4,
                    height=2,
                    command=lambda row=i, col=j: self.player_move(row, col),
                    bg="lightgray",
                    relief="raised",
                    bd=2
                )
                button.grid(row=i, column=j, padx=2, pady=2)
                button_row.append(button)
            self.buttons.append(button_row)
        
        # Control buttons frame
        control_frame = tk.Frame(self.root)
        control_frame.pack(pady=20)
        
        # New Game button
        new_game_btn = tk.Button(
            control_frame,
            text="New Game",
            font=("Arial", 12),
            command=self.reset_game,
            bg="lightblue",
            padx=20
        )
        new_game_btn.pack(side=tk.LEFT, padx=10)
        
        # Quit button
        quit_btn = tk.Button(
            control_frame,
            text="Quit",
            font=("Arial", 12),
            command=self.root.quit,
            bg="lightcoral",
            padx=20
        )
        quit_btn.pack(side=tk.LEFT, padx=10)
        
    def player_move(self, row, col):
        """Handle player's move when a button is clicked."""
        # Check if the game is over or cell is already occupied
        if self.game_over or self.board[row][col] != "":
            return
        
        # Make the player's move
        self.board[row][col] = self.current_player
        self.buttons[row][col].config(
            text=self.current_player,
            state="disabled",
            bg="lightblue",
            disabledforeground="blue"
        )
        
        # Check if player won
        if self.check_winner():
            self.end_game(f"Congratulations! You won!")
            self.player_score += 1
            return
        
        # Check for tie
        if self.is_board_full():
            self.end_game("It's a tie!")
            self.ties += 1
            return
        
        # Computer's turn
        self.status_label.config(text="Computer is thinking...")
        self.root.after(500, self.computer_move)  # Delay for better UX
        
    def computer_move(self):
        """Handle computer's move with basic AI strategy."""
        if self.game_over:
            return
        
        # Try to win first
        move = self.find_winning_move(self.computer_player)
        if move:
            row, col = move
        else:
            # Try to block player from winning
            move = self.find_winning_move(self.current_player)
            if move:
                row, col = move
            else:
                # Choose random empty cell
                empty_cells = [(i, j) for i in range(3) for j in range(3) if self.board[i][j] == ""]
                if empty_cells:
                    row, col = random.choice(empty_cells)
                else:
                    return
        
        # Make the computer's move
        self.board[row][col] = self.computer_player
        self.buttons[row][col].config(
            text=self.computer_player,
            state="disabled",
            bg="lightcoral",
            disabledforeground="red"
        )
        
        # Check if computer won
        if self.check_winner():
            self.end_game("Computer wins! Better luck next time!")
            self.computer_score += 1
            return
        
        # Check for tie
        if self.is_board_full():
            self.end_game("It's a tie!")
            self.ties += 1
            return
        
        # Player's turn again
        self.status_label.config(text="Your turn! Click a cell to place X")
        
    def find_winning_move(self, player):
        """Find a winning move for the given player."""
        for i in range(3):
            for j in range(3):
                if self.board[i][j] == "":
                    # Try this move
                    self.board[i][j] = player
                    if self.check_winner():
                        self.board[i][j] = ""  # Undo the move
                        return (i, j)
                    self.board[i][j] = ""  # Undo the move
        return None
        
    def check_winner(self):
        """Check if there's a winner on the current board."""
        # Check rows
        for row in self.board:
            if row[0] == row[1] == row[2] != "":
                return True
        
        # Check columns
        for col in range(3):
            if self.board[0][col] == self.board[1][col] == self.board[2][col] != "":
                return True
        
        # Check diagonals
        if self.board[0][0] == self.board[1][1] == self.board[2][2] != "":
            return True
        if self.board[0][2] == self.board[1][1] == self.board[2][0] != "":
            return True
        
        return False
        
    def is_board_full(self):
        """Check if the board is completely filled."""
        for row in self.board:
            for cell in row:
                if cell == "":
                    return False
        return True
        
    def end_game(self, message):
        """End the current game and show result."""
        self.game_over = True
        self.status_label.config(text=message)
        
        # Disable all buttons
        for i in range(3):
            for j in range(3):
                self.buttons[i][j].config(state="disabled")
        
        # Update score display
        self.update_score_display()
        
        # Show result popup
        result = messagebox.askyesno("Game Over", f"{message}\n\nWould you like to play again?")
        if result:
            self.reset_game()
        
    def update_score_display(self):
        """Update the score display."""
        self.score_label.config(
            text=f"Player (X): {self.player_score} | Computer (O): {self.computer_score} | Ties: {self.ties}"
        )
        
    def reset_game(self):
        """Reset the game to start a new round."""
        # Reset game state
        self.board = [["" for _ in range(3)] for _ in range(3)]
        self.game_over = False
        
        # Reset all buttons
        for i in range(3):
            for j in range(3):
                self.buttons[i][j].config(
                    text="",
                    state="normal",
                    bg="lightgray"
                )
        
        # Reset status
        self.status_label.config(text="Your turn! Click a cell to place X")
        
    def run(self):
        """Start the game loop."""
        self.root.mainloop()

# Create and run the game
if __name__ == "__main__":
    game = TicTacToeGame()
    game.run()
